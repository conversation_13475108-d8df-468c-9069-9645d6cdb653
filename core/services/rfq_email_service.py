"""RFQ Email Service for Talaria Dashboard.

This service handles the automation of RFQ (Request for Quotation) emails
by reading Excel files and sending formatted emails to specified recipients.

Developed for Ligentec SA - RFQ Email Automation Feature
"""

import logging
import os
import re
from datetime import datetime
from typing import Dict, List, Optional, Tuple

import pandas as pd
from flask import current_app
from flask_mail import Message
from werkzeug.utils import secure_filename

# Configure logging
logger = logging.getLogger(__name__)

# Email configuration constants
RFQ_SENDER = "<EMAIL>"
RFQ_RECIPIENTS_PRODUCTION = ["<EMAIL>"]
RFQ_CC_PRODUCTION = ["Jean-<PERSON>.<PERSON><EMAIL>", "<EMAIL>"]
RFQ_RECIPIENTS_TEST = ["<EMAIL>"]
RFQ_CC_TEST = []

# File upload configuration
ALLOWED_EXTENSIONS = {"xlsx", "xls"}
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
UPLOAD_FOLDER = "uploads/rfq"

# Email template
EMAIL_SUBJECT = "RFQ Quotation Request LigentecFrance"
EMAIL_SIGNATURE = """
Best regards,

Elisée Kajingu
Test Technician
LIGENTEC SAS France
224 Boulevard John Kennedy
91100 Corbeil-Essonnes, France

Tel: +33 (0)1 XX XX XX XX
Email: <EMAIL>

---
🚀 Powered by Talaria - Automated Email System
Streamlining communication for efficient project management
"""


class RFQEmailService:
    """Service class for handling RFQ email automation."""

    def __init__(self, mail_instance):
        """Initialize the RFQ Email Service.

        Args:
            mail_instance: Flask-Mail instance for sending emails
        """
        self.mail = mail_instance
        self.upload_folder = os.path.join(current_app.root_path, UPLOAD_FOLDER)
        self._ensure_upload_folder()

    def _ensure_upload_folder(self):
        """Ensure the upload folder exists."""
        try:
            os.makedirs(self.upload_folder, exist_ok=True)
            logger.info(f"Upload folder ready: {self.upload_folder}")
        except Exception as e:
            logger.error(f"Failed to create upload folder: {str(e)}")
            raise

    def validate_file(self, file) -> Tuple[bool, str]:
        """Validate uploaded Excel file.

        Args:
            file: Uploaded file object

        Returns:
            Tuple of (is_valid, error_message)
        """
        if not file or not file.filename:
            return False, "No file selected"

        # Check file extension
        if not self._allowed_file(file.filename):
            return False, "Invalid file type. Please upload .xlsx or .xls files only"

        # Check file size (approximate)
        file.seek(0, 2)  # Seek to end
        size = file.tell()
        file.seek(0)  # Reset to beginning

        if size > MAX_FILE_SIZE:
            return (
                False,
                f"File too large. Maximum size is {MAX_FILE_SIZE // (1024*1024)}MB",
            )

        return True, ""

    def _allowed_file(self, filename: str) -> bool:
        """Check if file extension is allowed."""
        return (
            "." in filename and filename.rsplit(".", 1)[1].lower() in ALLOWED_EXTENSIONS
        )

    def save_uploaded_file(self, file) -> str:
        """Save uploaded file securely.

        Args:
            file: Uploaded file object

        Returns:
            Path to saved file
        """
        filename = secure_filename(file.filename)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        unique_filename = f"rfq_{timestamp}_{filename}"
        file_path = os.path.join(self.upload_folder, unique_filename)

        try:
            file.save(file_path)
            logger.info(f"File saved: {file_path}")
            return file_path
        except Exception as e:
            logger.error(f"Failed to save file: {str(e)}")
            raise

    def parse_excel_file(self, file_path: str) -> List[Dict]:
        """Parse Excel file and extract RFQ data.

        Args:
            file_path: Path to Excel file

        Returns:
            List of RFQ records
        """
        try:
            # Read Excel file
            df = pd.read_excel(file_path)
            logger.info(f"Excel file loaded with {len(df)} rows")

            # Validate required columns
            required_columns = ["Project_Name", "Links"]
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                raise ValueError(
                    f"Missing required columns: {', '.join(missing_columns)}"
                )

            # Process each row
            rfq_records = []
            for index, row in df.iterrows():
                try:
                    record = self._process_row(row, index + 1)
                    if record:
                        rfq_records.append(record)
                except Exception as e:
                    logger.warning(f"Skipping row {index + 1}: {str(e)}")
                    continue

            logger.info(f"Processed {len(rfq_records)} valid RFQ records")
            return rfq_records

        except Exception as e:
            logger.error(f"Failed to parse Excel file: {str(e)}")
            raise

    def _process_row(self, row, row_number: int) -> Optional[Dict]:
        """Process a single Excel row.

        Args:
            row: Pandas Series representing a row
            row_number: Row number for error reporting

        Returns:
            Processed RFQ record or None if invalid
        """
        order_id = str(row.get("Order_ID", "")).strip()
        project_name = str(row.get("Project_Name", "")).strip()
        links_raw = str(row.get("Links", "")).strip()

        # Validate required fields
        if not project_name or not links_raw:
            raise ValueError(f"Missing required data in row {row_number}")

        # Extract and validate links
        links = self._extract_links(links_raw)
        if not links:
            raise ValueError(f"No valid links found in row {row_number}")

        if len(links) > 10:
            logger.warning(
                f"Row {row_number}: More than 10 links found, using first 10"
            )
            links = links[:10]

        return {
            "order_id": order_id if order_id else f"AUTO_{row_number:03d}",
            "project_name": project_name,
            "links": links,
            "priority": str(row.get("Priority", "Medium")).strip(),
            "notes": str(row.get("Notes", "")).strip(),
        }

    def _extract_links(self, links_text: str) -> List[str]:
        """Extract valid URLs from text.

        Args:
            links_text: Text containing URLs

        Returns:
            List of valid URLs
        """
        # Split by common separators
        potential_links = re.split(r"[,;\n\r\t]+", links_text)

        # URL pattern - enhanced for XFab cloud URLs with special characters
        url_pattern = re.compile(
            r"https?://(?:[-\w.])+(?:[:\d]+)?(?:/(?:[\w/_.-])*(?:\?(?:[\w&=%/.-])*)?(?:#(?:[\w.-])*)?)?",
            re.IGNORECASE,
        )

        valid_links = []
        for link in potential_links:
            link = link.strip()
            if url_pattern.match(link):
                valid_links.append(link)

        return valid_links

    def send_rfq_emails(
        self, rfq_records: List[Dict], file_path: str, test_mode: bool = True
    ) -> Dict:
        """Send a single consolidated RFQ email with all projects.

        Args:
            rfq_records: List of RFQ records to process
            file_path: Path to the Excel file to attach
            test_mode: If True, send only to test recipients

        Returns:
            Summary of email sending results
        """
        results = {
            "total_records": len(rfq_records),
            "emails_sent": 0,
            "errors": [],
            "test_mode": test_mode,
        }

        try:
            # Send one consolidated email with all projects
            self._send_consolidated_rfq_email(rfq_records, file_path, test_mode)
            results["emails_sent"] = 1
            logger.info(f"Consolidated RFQ email sent with {len(rfq_records)} projects")
        except Exception as e:
            error_msg = f"Failed to send consolidated RFQ email: {str(e)}"
            results["errors"].append(error_msg)
            logger.error(error_msg)

        return results

    def _send_consolidated_rfq_email(
        self, rfq_records: List[Dict], file_path: str, test_mode: bool
    ):
        """Send a single consolidated RFQ email with all projects.

        Args:
            rfq_records: List of all RFQ records to include
            file_path: Path to the Excel file to attach
            test_mode: If True, send only to test recipients
        """
        # Determine recipients based on mode
        if test_mode:
            recipients = RFQ_RECIPIENTS_TEST
            cc_recipients = RFQ_CC_TEST
        else:
            recipients = RFQ_RECIPIENTS_PRODUCTION
            cc_recipients = RFQ_CC_PRODUCTION

        # Create clean, professional email body without project details
        # (projects and links are in the Excel attachment)
        body = f"""Dear Pascale,

Could you please send us a quotation for the projects detailed in the attached Excel file.

The Excel file contains all project information including names and direct links to the project folders for your convenience.

Thank you for your time and assistance.
{EMAIL_SIGNATURE}"""

        # Create and send message
        msg = Message(
            subject=EMAIL_SUBJECT,
            recipients=recipients,
            cc=cc_recipients,
            body=body,
            sender=RFQ_SENDER,
        )

        # Attach the Excel file
        try:
            with open(file_path, "rb") as f:
                msg.attach(
                    filename=os.path.basename(file_path),
                    content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    data=f.read(),
                )
        except Exception as e:
            logger.warning(f"Could not attach file {file_path}: {str(e)}")

        self.mail.send(msg)

    def _send_single_rfq_email(self, record: Dict, file_path: str, test_mode: bool):
        """Send a single RFQ email (legacy method for test emails).

        Args:
            record: RFQ record data
            file_path: Path to the Excel file to attach
            test_mode: If True, send only to test recipients
        """
        # Use the consolidated method with a single record for consistency
        self._send_consolidated_rfq_email([record], file_path, test_mode)

    def cleanup_old_files(self, days_old: int = 30):
        """Clean up old uploaded files.

        Args:
            days_old: Remove files older than this many days
        """
        try:
            cutoff_time = datetime.now().timestamp() - (days_old * 24 * 60 * 60)

            for filename in os.listdir(self.upload_folder):
                file_path = os.path.join(self.upload_folder, filename)
                if (
                    os.path.isfile(file_path)
                    and os.path.getmtime(file_path) < cutoff_time
                ):
                    os.remove(file_path)
                    logger.info(f"Removed old file: {filename}")

        except Exception as e:
            logger.error(f"Error during file cleanup: {str(e)}")
