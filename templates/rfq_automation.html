{% extends "base.html" %} {% block title %}RFQ Email Automation - Talaria
Dashboard{% endblock %} {% block extra_css %}
<style>
  .upload-area {
    border: 2px dashed #cbd5e0;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    background-color: #f7fafc;
  }

  .upload-area:hover {
    border-color: #4299e1;
    background-color: #ebf8ff;
  }

  .upload-area.dragover {
    border-color: #3182ce;
    background-color: #bee3f8;
  }

  .file-info {
    background-color: #f0fff4;
    border: 1px solid #9ae6b4;
    border-radius: 6px;
    padding: 1rem;
    margin-top: 1rem;
  }

  .preview-table {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .mode-toggle {
    background-color: #fed7d7;
    border: 2px solid #fc8181;
    color: #c53030;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: bold;
  }

  .mode-toggle.production {
    background-color: #c6f6d5;
    border-color: #68d391;
    color: #2f855a;
  }

  .progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    background-color: #4299e1;
    transition: width 0.3s ease;
  }
</style>
{% endblock %} {% block content %}
<div class="container mx-auto px-4 py-6">
  <!-- Header -->
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
      <i class="fas fa-envelope-open-text mr-3 text-blue-600"></i>
      RFQ Email Automation
    </h1>
    <p class="text-gray-600 dark:text-gray-300">
      Automate RFQ quotation emails by uploading an Excel file with project
      links.
    </p>
  </div>

  <!-- Data Persistence Banner -->
  {% if existing_data %}
  <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6">
    <div class="flex">
      <div class="flex-shrink-0">
        <i class="fas fa-info-circle text-blue-400"></i>
      </div>
      <div class="ml-3">
        <p class="text-sm text-blue-700">
          <strong>Previous session restored!</strong> Your RFQ data persists
          while you're logged in. Found
          <strong>{{ existing_data.records_count }}</strong> records in
          <strong>{{ existing_data.file_name }}</strong> ({{
          existing_data.file_size }} MB).
          <br />
          <span class="text-xs"
            >You can navigate to other pages and return without re-uploading.
            Use "Clear & Start Fresh" for new tasks.</span
          >
        </p>
      </div>
    </div>
  </div>
  {% endif %}

  <!-- Test Mode Warning -->
  <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
    <div class="flex">
      <div class="flex-shrink-0">
        <i class="fas fa-exclamation-triangle text-yellow-400"></i>
      </div>
      <div class="ml-3">
        <p class="text-sm text-yellow-700">
          <strong>Test Mode Active:</strong> All emails are in test mode.
        </p>
      </div>
    </div>
  </div>

  <!-- Upload Section -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
    <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
      <i class="fas fa-upload mr-2"></i>Upload RFQ Excel File
    </h2>

    <div class="upload-area" id="uploadArea">
      <div class="mb-4">
        <i class="fas fa-file-excel text-4xl text-green-500 mb-2"></i>
        <p class="text-lg font-medium text-gray-700">
          Drop your Excel file here or click to browse
        </p>
        <p class="text-sm text-gray-500 mt-2">
          Supported formats: .xlsx, .xls (Max size: 10MB)
        </p>
      </div>

      <input type="file" id="rfqFile" accept=".xlsx,.xls" class="hidden" />
      <button
        type="button"
        id="browseBtn"
        class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg"
      >
        <i class="fas fa-folder-open mr-2"></i>Browse Files
      </button>
    </div>

    <!-- File Info -->
    <div id="fileInfo" class="file-info hidden">
      <div class="flex items-center justify-between">
        <div>
          <p class="font-medium text-green-700">
            <i class="fas fa-check-circle mr-2"></i>
            <span id="fileName"></span>
          </p>
          <p class="text-sm text-green-600" id="fileDetails"></p>
        </div>
        <button
          type="button"
          id="removeFile"
          class="text-red-500 hover:text-red-700"
        >
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>

    <!-- Upload Progress -->
    <div id="uploadProgress" class="hidden mt-4">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm text-gray-600">Uploading...</span>
        <span class="text-sm text-gray-600" id="progressPercent">0%</span>
      </div>
      <div class="progress-bar">
        <div class="progress-fill" id="progressFill" style="width: 0%"></div>
      </div>
    </div>
  </div>

  <!-- Excel Format Guide -->
  <div class="bg-blue-50 dark:bg-blue-900 rounded-lg p-6 mb-6">
    <div class="flex justify-between items-start mb-3">
      <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100">
        <i class="fas fa-info-circle mr-2"></i>Excel File Format Requirements
      </h3>
      <a
        href="{{ url_for('rfq.download_template') }}"
        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm"
      >
        <i class="fas fa-download mr-2"></i>Download Template
      </a>
    </div>
    <div class="grid md:grid-cols-2 gap-4">
      <div>
        <h4 class="font-medium text-blue-800 dark:text-blue-200 mb-2">
          Required Columns:
        </h4>
        <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
          <li><strong>Project_Name:</strong> Name of the project</li>
          <li>
            <strong>Links:</strong> XFab cloud URLs (1-10 per project,
            comma-separated)
          </li>
        </ul>
      </div>
      <div>
        <h4 class="font-medium text-blue-800 dark:text-blue-200 mb-2">
          Optional Columns:
        </h4>
        <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
          <li><strong>Order_ID:</strong> Optional identifier for tracking</li>
          <li><strong>Priority:</strong> High, Medium, Low</li>
          <li><strong>Notes:</strong> Additional comments</li>
        </ul>
      </div>
    </div>
    <div class="mt-4 p-3 bg-blue-100 dark:bg-blue-800 rounded-lg">
      <h4 class="font-medium text-blue-800 dark:text-blue-200 mb-2">
        Example XFab Cloud Link:
      </h4>
      <code class="text-xs text-blue-700 dark:text-blue-300 break-all">
        https://cloud.xfab.com/index.php/s/A3on9tSzbQ2ctkt?path=%2F1-Operations%2FRFQ_SoW_batch_ID%2FAN350_05-114_femto_icomb_run1&openfile=30220756
      </code>
      <p class="text-xs text-blue-600 dark:text-blue-400 mt-2">
        <strong>Multiple Links:</strong> Separate with commas or line breaks in
        the Links column
      </p>
    </div>
  </div>

  <!-- Preview Section -->
  <div
    id="previewSection"
    class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6 hidden"
  >
    <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
      <i class="fas fa-eye mr-2"></i>Preview RFQ Records
    </h2>

    <div class="preview-table">
      <table class="w-full">
        <thead class="bg-gray-50 dark:bg-gray-700">
          <tr>
            <th
              class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
            >
              Order ID
            </th>
            <th
              class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
            >
              Project Name
            </th>
            <th
              class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
            >
              Links Count
            </th>
            <th
              class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
            >
              Priority
            </th>
          </tr>
        </thead>
        <tbody
          id="previewTableBody"
          class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"
        >
          <!-- Preview data will be inserted here -->
        </tbody>
      </table>
    </div>

    <div class="mt-4 text-sm text-gray-600 dark:text-gray-400">
      <span id="totalRecords"></span> total records found. Showing first 5
      records.
    </div>

    <!-- Email Body Preview -->
    <div class="mt-6 p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
      <h4 class="font-medium text-blue-800 dark:text-blue-200 mb-3">
        <i class="fas fa-envelope mr-2"></i>Email Body Preview
      </h4>
      <div
        class="text-sm text-blue-700 dark:text-blue-300 bg-white dark:bg-blue-800 p-3 rounded border"
      >
        <p><strong>Subject:</strong> RFQ Quotation Request LigentecFrance</p>
        <br />
        <div class="whitespace-pre-line text-left">
          Dear Pascale, Could you please send us a quotation for the projects
          detailed in the attached Excel file. The Excel file contains all
          project information including names and direct links to the project
          folders for your convenience. Thank you for your time and assistance.
          Best regards, Elisée Kajingu Test Technician LIGENTEC SAS France 224
          Boulevard John Kennedy 91100 Corbeil-Essonnes, France Tel: +33 (0)1 XX
          XX XX XX Email: <EMAIL> --- 🚀 Powered by Talaria - Automated
          Email System Streamlining communication for efficient project
          management
        </div>
      </div>
      <p class="text-xs text-blue-600 dark:text-blue-400 mt-2">
        <i class="fas fa-paperclip mr-1"></i>
        The uploaded Excel file will be attached to the email.
      </p>
    </div>
  </div>

  <!-- Email Sending Section -->
  <div
    id="sendSection"
    class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hidden"
  >
    <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
      <i class="fas fa-paper-plane mr-2"></i>Send RFQ Email
    </h2>

    <!-- Mode Toggle -->
    <div class="mb-6">
      <label
        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
        >Email Mode:</label
      >
      <div class="flex items-center space-x-4">
        <button type="button" id="testModeBtn" class="mode-toggle">
          <i class="fas fa-flask mr-2"></i>Test Mode (<EMAIL>)
        </button>
        <button
          type="button"
          id="prodModeBtn"
          class="mode-toggle production hidden"
        >
          <i class="fas fa-rocket mr-2"></i>Production Mode (Live Recipients)
        </button>
      </div>
      <p class="text-xs text-gray-500 mt-2">
        Test mode sends emails <NAME_EMAIL> for validation.
      </p>
    </div>

    <!-- Action Buttons -->
    <div class="flex space-x-4">
      <button
        type="button"
        id="sendTestBtn"
        class="bg-yellow-500 hover:bg-yellow-600 text-white px-6 py-2 rounded-lg"
      >
        <i class="fas fa-vial mr-2"></i>Send Test Email
      </button>
      <button
        type="button"
        id="sendAllBtn"
        class="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg"
      >
        <i class="fas fa-envelope mr-2"></i>Send RFQ Email
      </button>
      <button
        type="button"
        id="clearSessionBtn"
        class="bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded-lg"
        title="Clear current data and start fresh"
      >
        <i class="fas fa-trash mr-2"></i>Clear & Start Fresh
      </button>
    </div>
  </div>

  <!-- Results Section -->
  <div
    id="resultsSection"
    class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hidden"
  >
    <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
      <i class="fas fa-chart-bar mr-2"></i>Email Sending Results
    </h2>
    <div id="resultsContent">
      <!-- Results will be displayed here -->
    </div>
  </div>
</div>

<!-- Loading Modal -->
<div
  id="loadingModal"
  class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden"
>
  <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-sm w-full mx-4">
    <div class="text-center">
      <div
        class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"
      ></div>
      <p class="text-gray-700 dark:text-gray-300" id="loadingText">
        Processing...
      </p>
    </div>
  </div>
</div>
{% endblock %} {% block extra_js %}
<script>
  document.addEventListener("DOMContentLoaded", function () {
    // DOM elements
    const uploadArea = document.getElementById("uploadArea");
    const fileInput = document.getElementById("rfqFile");
    const browseBtn = document.getElementById("browseBtn");
    const fileInfo = document.getElementById("fileInfo");
    const fileName = document.getElementById("fileName");
    const fileDetails = document.getElementById("fileDetails");
    const removeFileBtn = document.getElementById("removeFile");
    const uploadProgress = document.getElementById("uploadProgress");
    const progressFill = document.getElementById("progressFill");
    const progressPercent = document.getElementById("progressPercent");
    const previewSection = document.getElementById("previewSection");
    const sendSection = document.getElementById("sendSection");
    const resultsSection = document.getElementById("resultsSection");
    const loadingModal = document.getElementById("loadingModal");
    const loadingText = document.getElementById("loadingText");

    // Buttons
    const testModeBtn = document.getElementById("testModeBtn");
    const prodModeBtn = document.getElementById("prodModeBtn");
    const sendTestBtn = document.getElementById("sendTestBtn");
    const sendAllBtn = document.getElementById("sendAllBtn");
    const clearSessionBtn = document.getElementById("clearSessionBtn");

    let currentMode = "test";
    let uploadedFile = null;

    // Check for existing data on page load
    const existingData = {{ existing_data | tojson | safe }};
    if (existingData) {
      console.log("Found existing RFQ data:", existingData);
      restoreExistingData(existingData);
    }

    // File upload handling
    browseBtn.addEventListener("click", () => fileInput.click());

    fileInput.addEventListener("change", handleFileSelect);

    // Drag and drop
    uploadArea.addEventListener("dragover", (e) => {
      e.preventDefault();
      uploadArea.classList.add("dragover");
    });

    uploadArea.addEventListener("dragleave", () => {
      uploadArea.classList.remove("dragover");
    });

    uploadArea.addEventListener("drop", (e) => {
      e.preventDefault();
      uploadArea.classList.remove("dragover");
      const files = e.dataTransfer.files;
      if (files.length > 0) {
        fileInput.files = files;
        handleFileSelect();
      }
    });

    removeFileBtn.addEventListener("click", resetUpload);

    // Mode toggle
    testModeBtn.addEventListener("click", () => setMode("test"));
    prodModeBtn.addEventListener("click", () => setMode("production"));

    // Email sending
    sendTestBtn.addEventListener("click", sendTestEmail);
    sendAllBtn.addEventListener("click", sendAllEmails);
    clearSessionBtn.addEventListener("click", clearSession);

    function handleFileSelect() {
      const file = fileInput.files[0];
      if (!file) return;

      uploadedFile = file;

      // Show file info
      fileName.textContent = file.name;
      fileDetails.textContent = `Size: ${(file.size / 1024 / 1024).toFixed(
        2
      )} MB`;
      fileInfo.classList.remove("hidden");

      // Upload file
      uploadFile(file);
    }

    function uploadFile(file) {
      const formData = new FormData();
      formData.append("rfq_file", file);

      console.log("Starting file upload...");
      showLoading("Uploading and processing file...");
      uploadProgress.classList.remove("hidden");

      fetch("/rfq/upload", {
        method: "POST",
        body: formData,
      })
        .then((response) => {
          console.log("Upload response status:", response.status);
          return response.json();
        })
        .then((data) => {
          console.log("Upload response data:", data);
          hideLoading();
          uploadProgress.classList.add("hidden");

          if (data.success) {
            console.log("Upload successful, showing preview...");
            showPreview(data);
            showSuccess(data.message);
          } else {
            console.log("Upload failed:", data.message);
            showError(data.message);
            resetUpload();
          }
        })
        .catch((error) => {
          console.error("Upload error:", error);
          hideLoading();
          uploadProgress.classList.add("hidden");
          showError("Error uploading file: " + error.message);
          resetUpload();
        });
    }

    function showPreview(data) {
      console.log("showPreview called with data:", data);

      // Populate preview table
      const tbody = document.getElementById("previewTableBody");
      if (!tbody) {
        console.error("previewTableBody element not found!");
        return;
      }

      tbody.innerHTML = "";

      if (data.preview_data && data.preview_data.length > 0) {
        data.preview_data.forEach((record) => {
          const row = document.createElement("tr");
          row.innerHTML = `
                  <td class="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">${record.order_id}</td>
                  <td class="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">${record.project_name}</td>
                  <td class="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">${record.links_count}</td>
                  <td class="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">${record.priority}</td>
              `;
          tbody.appendChild(row);
        });
      } else {
        console.warn("No preview data found");
      }

      const totalRecordsEl = document.getElementById("totalRecords");
      if (totalRecordsEl) {
        totalRecordsEl.textContent = data.records_count;
      }

      console.log("Showing preview and send sections...");
      if (previewSection) {
        previewSection.classList.remove("hidden");
        console.log("Preview section shown");
      } else {
        console.error("previewSection element not found!");
      }

      if (sendSection) {
        sendSection.classList.remove("hidden");
        console.log("Send section shown");
      } else {
        console.error("sendSection element not found!");
      }
    }

    function setMode(mode) {
      currentMode = mode;
      if (mode === "test") {
        testModeBtn.classList.remove("hidden");
        prodModeBtn.classList.add("hidden");
      } else {
        testModeBtn.classList.add("hidden");
        prodModeBtn.classList.remove("hidden");
      }
    }

    function sendTestEmail() {
      showLoading("Sending test email...");

      fetch("/rfq/test-email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      })
        .then((response) => response.json())
        .then((data) => {
          hideLoading();
          if (data.success) {
            showSuccess(data.message);
          } else {
            showError(data.message);
          }
        })
        .catch((error) => {
          hideLoading();
          showError("Error sending test email: " + error.message);
        });
    }

    function sendAllEmails() {
      const modeText =
        currentMode === "test"
          ? "TEST MODE (<EMAIL> only)"
          : "PRODUCTION MODE (Live Recipients)";

      Swal.fire({
        title: "Send RFQ Email?",
        text: `Are you sure you want to send the RFQ email in ${modeText}?`,
        icon: "question",
        showCancelButton: true,
        confirmButtonColor: "#28a745",
        cancelButtonColor: "#6c757d",
        confirmButtonText: "Yes, Send Email!",
        cancelButtonText: "Cancel",
      }).then((result) => {
        if (result.isConfirmed) {
          showLoading("Sending RFQ email...");

          fetch("/rfq/send-emails", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              test_mode: currentMode === "test",
            }),
          })
            .then((response) => response.json())
            .then((data) => {
              hideLoading();
              showResults(data);
            })
            .catch((error) => {
              hideLoading();
              showError("Error sending emails: " + error.message);
            });
        }
      });
    }

    function showResults(data) {
      const resultsContent = document.getElementById("resultsContent");

      if (data.success) {
        resultsContent.innerHTML = `
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h3 class="text-lg font-medium text-green-800 mb-2">
                        <i class="fas fa-check-circle mr-2"></i>RFQ Email Sent Successfully
                    </h3>
                    <div class="text-sm text-green-700">
                        <p><strong>Total Records:</strong> ${
                          data.results.total_records
                        }</p>
                        <p><strong>Emails Sent:</strong> ${
                          data.results.emails_sent
                        }</p>
                        <p><strong>Errors:</strong> ${
                          data.results.errors_count
                        }</p>
                        <p><strong>Mode:</strong> ${
                          data.results.test_mode
                            ? "Test Mode"
                            : "Production Mode"
                        }</p>
                    </div>
                    ${
                      data.results.errors.length > 0
                        ? `
                        <div class="mt-3">
                            <p class="font-medium text-red-700">Errors:</p>
                            <ul class="text-sm text-red-600 list-disc list-inside">
                                ${data.results.errors
                                  .map((error) => `<li>${error}</li>`)
                                  .join("")}
                            </ul>
                        </div>
                    `
                        : ""
                    }
                </div>
            `;
      } else {
        resultsContent.innerHTML = `
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <h3 class="text-lg font-medium text-red-800 mb-2">
                        <i class="fas fa-exclamation-circle mr-2"></i>Email Sending Failed
                    </h3>
                    <p class="text-sm text-red-700">${data.message}</p>
                </div>
            `;
      }

      resultsSection.classList.remove("hidden");
      resetUpload();
    }

    function resetUpload() {
      uploadedFile = null;
      fileInput.value = "";
      fileInfo.classList.add("hidden");
      previewSection.classList.add("hidden");
      sendSection.classList.add("hidden");
      uploadProgress.classList.add("hidden");
    }

    function showLoading(text) {
      loadingText.textContent = text;
      loadingModal.classList.remove("hidden");
    }

    function hideLoading() {
      loadingModal.classList.add("hidden");
    }

    function showSuccess(message) {
      Swal.fire({
        icon: "success",
        title: "Success!",
        text: message,
        timer: 3000,
        showConfirmButton: false,
        toast: true,
        position: "top-end",
      });
    }

    function showError(message) {
      Swal.fire({
        icon: "error",
        title: "Error!",
        text: message,
        confirmButtonText: "OK",
        confirmButtonColor: "#dc3545",
      });
    }

    function restoreExistingData(data) {
      console.log("Restoring existing data:", data);

      // Show file info
      fileName.textContent = data.file_name;
      fileDetails.textContent = `Size: ${data.file_size} MB`;
      fileInfo.classList.remove("hidden");

      // Show preview with existing data
      showPreview({
        success: true,
        records_count: data.records_count,
        preview_data: data.preview_data,
        message: `Restored previous session: ${data.records_count} RFQ records found.`
      });

      showSuccess(`Previous session restored! Found ${data.records_count} RFQ records.`);
    }

    function clearSession() {
      Swal.fire({
        title: "Clear Session Data?",
        text: "This will remove your current RFQ data and you'll need to upload a new file.",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#dc3545",
        cancelButtonColor: "#6c757d",
        confirmButtonText: "Yes, Clear Data!",
        cancelButtonText: "Cancel",
      }).then((result) => {
        if (result.isConfirmed) {
          showLoading("Clearing session data...");

          fetch("/rfq/clear-session", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
          })
            .then((response) => response.json())
            .then((data) => {
              hideLoading();
              if (data.success) {
                resetUpload();
                showSuccess("Session cleared! You can now upload a new file.");
              } else {
                showError(data.message);
              }
            })
            .catch((error) => {
              hideLoading();
              showError("Error clearing session: " + error.message);
            });
        }
      });
    }
  });
</script>
{% endblock %}
